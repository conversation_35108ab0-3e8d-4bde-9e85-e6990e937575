"use client";

import React from "react";
import { Calendar as CalendarIcon, MapPin, Trophy, Award, X, AlertCircle } from "lucide-react";
import { Tournament } from "@/types/calendar";
import { Colors } from "@/lib/constants/colors";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { useTournamentResults } from "@/lib/hooks/useTournamentResults";
import { Spinner } from "@/components/ui/Spinner";

interface TournamentResultsModalProps { 
  tournament: Tournament | null;
  isOpen: boolean;
  onClose: () => void;
}

export function TournamentResultsModal({ 
  tournament, 
  isOpen, 
  onClose 
}: TournamentResultsModalProps) {
  const { data: results, isLoading, error } = useTournamentResults(tournament?.id || null);
  
  if (!isOpen || !tournament) return null;
  
  const storeColor = Colors.getStoreScheme(tournament.store, tournament);
  
  // Format results for display
  const displayResults = results?.map(result => ({
    position: result.position,
    name: result.player?.name || '<PERSON><PERSON><PERSON><PERSON>',
    deck: result.deck || 'N/A', // Deck info not yet in database
    points: result.points
  })) || [];

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 rounded-xl border border-blue-500/30 w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header del Modale */}
        <div className="p-3 sm:p-4 border-b border-blue-500/20 flex justify-between items-center">
          <h3 className="text-lg sm:text-xl font-bold">Risultati Torneo - {tournament.title}</h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-blue-900/50 rounded-full transition-colors"
          >
            <X size={18} className="sm:w-5 sm:h-5" />
          </button>
        </div>

        {/* Contenuto del Modale */}
        <div className="p-3 sm:p-4 overflow-y-auto max-h-[calc(95vh-8rem)] sm:max-h-[calc(90vh-8rem)]">
          {/* Riepilogo Torneo */}
          <div className={`mb-4 p-3 rounded-lg bg-black/30 border ${storeColor.border}`}>
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
              <div className="flex items-center gap-2">
                <CalendarIcon size={16} className={storeColor.light} />
                <span className="text-sm">{format(new Date(tournament.date), "d MMMM yyyy", { locale: it })}</span>
              </div>
              {tournament.store && (
                <div className="flex items-center gap-2">
                  <MapPin size={16} className={storeColor.light} />
                  <span className="text-sm">{tournament.store.name}</span>
                </div>
              )}
            </div>
          </div>
          
          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <Spinner />
            </div>
          )}
          
          {/* Error State */}
          {error && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 mb-4">
              <div className="flex items-center gap-2">
                <AlertCircle size={18} className="text-red-400" />
                <p className="text-sm text-red-300">Errore nel caricamento dei risultati</p>
              </div>
            </div>
          )}
          
          {/* No Results State */}
          {!isLoading && !error && displayResults.length === 0 && (
            <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 text-center">
              <p className="text-sm text-blue-300">Nessun risultato disponibile per questo torneo</p>
            </div>
          )}
          
          {/* Results Table - Only show if we have data */}
          {!isLoading && !error && displayResults.length > 0 && (
            <>
              {/* Classifica del torneo */}
              <h4 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <Trophy size={18} className="text-blue-300" />
                Classifica Finale
              </h4>
              
              <div className="bg-black/20 rounded-lg overflow-hidden mb-6">
                <table className="w-full">
                  <thead>
                    <tr className="bg-blue-900/30">
                      <th className="px-3 py-2 text-left text-xs font-medium text-blue-300">Pos.</th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-blue-300">Giocatore</th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-blue-300">Mazzo</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-blue-300">Punti</th>
                    </tr>
                  </thead>
                  <tbody>
                    {displayResults.map((result) => (
                      <tr key={result.position} className="border-t border-blue-500/20">
                        <td className="px-3 py-2">
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium
                            ${result.position === 1 ? "bg-yellow-500/20 text-yellow-300" : 
                              result.position === 2 ? "bg-gray-400/20 text-gray-300" : 
                              result.position === 3 ? "bg-amber-700/20 text-amber-600" : 
                              "bg-blue-500/20 text-blue-300"}`
                          }>
                            {result.position}
                          </div>
                        </td>
                        <td className="px-3 py-2 text-sm font-medium">{result.name}</td>
                        <td className="px-3 py-2 text-sm text-blue-300">{result.deck}</td>
                        <td className="px-3 py-2 text-sm font-medium text-right">{result.points}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* Miglior mazzo - Only show if we have a winner */}
              {displayResults.length > 0 && displayResults[0].deck !== 'N/A' && (
                <>
                  <h4 className="text-lg font-semibold mb-3 flex items-center gap-2">
                    <Award size={18} className="text-blue-300" />
                    Miglior Mazzo del Torneo
                  </h4>
                  <div className="bg-black/20 p-3 rounded-lg text-center mb-6">
                    <p className="font-semibold">{displayResults[0].deck}</p>
                    <p className="text-sm text-blue-300 mt-1">Giocato da {displayResults[0].name}</p>
                  </div>
                </>
              )}
            </>
          )}
        </div>

        {/* Footer del Modale */}
        <div className="p-3 sm:p-4 border-t border-blue-500/20 bg-black/20">
          <div className="flex justify-end">
            <button 
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors text-sm"
            >
              Chiudi
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
