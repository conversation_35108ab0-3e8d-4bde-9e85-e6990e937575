import { useState, useEffect, useCallback } from 'react';
import { Tournament } from '@/types/calendar';
import { useCreateTournament, useUpdateTournament } from '@/lib/hooks/useTournaments';
import { format } from 'date-fns';
import { logger } from '@/lib/utils/logger';

export interface Store {
  id: string;
  name: string;
  address: string;
  city: string;
}

export function useEventForm(initialData: Tournament | null, preselectedDate: Date | null, mode: 'create' | 'edit', isOpen: boolean) {
  const [formData, setFormData] = useState<Partial<Tournament>>({});
  const [stores, setStores] = useState<Store[]>([]);
  const [isStoresLoaded, setIsStoresLoaded] = useState(false);
  const createTournament = useCreateTournament();
  const updateTournament = useUpdateTournament();

  useEffect(() => {
    const fetchStores = async () => {
      if (!isOpen) return;
      try {
        const { supabase } = await import('@/lib/supabase/client');
        const { data, error } = await supabase.from('stores').select('id, name, address, city');
        if (error) {
          logger.error('Errore durante il caricamento dei negozi', error, { component: 'useEventForm' });
          return;
        }
        setStores(data || []);
        setIsStoresLoaded(true);
      } catch (error) {
        logger.error('Errore durante il caricamento dei negozi', error, { component: 'useEventForm' });
      }
    };
    fetchStores();
  }, [isOpen]);

  useEffect(() => {
    if (!isStoresLoaded) return;

    if (initialData) {
      let eventDate = format(new Date(), 'yyyy-MM-dd');
      if (initialData.date) {
        try {
          const dateObj = new Date(initialData.date);
          if (!isNaN(dateObj.getTime())) {
            eventDate = format(dateObj, 'yyyy-MM-dd');
          }
        } catch (e) {
          logger.error('Errore nella formattazione della data', e, { component: 'useEventForm' });
        }
      }
      const storeExists = stores.some(store => store.id === initialData.store_id);
      const storeId = storeExists ? initialData.store_id : '';
      setFormData({
        title: initialData.title || '',
        date: eventDate,
        time_start: initialData.time_start || '18:00:00',
        time_end: initialData.time_end || '22:00:00',
        format: initialData.format || 'Pauper',
        store_id: storeId,
        max_players: initialData.max_players || 32,
        price: initialData.price || 5,
        prize_pool: initialData.prize_pool || 'Premi in buoni negozio in base ai partecipanti',
        description: initialData.description || 'Torneo Pauper settimanale',
      });
    } else {
      const formattedDate = preselectedDate ? format(preselectedDate, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd');
      const newFormData = {
        title: '',
        date: formattedDate,
        time_start: '21:30:00',
        time_end: '01:00:00',
        format: 'Pauper',
        store_id: '',
        max_players: 30,
        price: 5,
        prize_pool: 'Premi in buoni negozio in base al numero di partecipanti',
        description: 'Torneo Pauper settimanale',
      };
      setFormData(newFormData);
      if (!newFormData.store_id && stores.length > 0) {
        setFormData(prev => ({ ...prev, store_id: stores[0].id }));
      }
    }
  }, [initialData, isOpen, isStoresLoaded, stores, preselectedDate]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  }, []);

  const handleNumberChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: parseInt(value, 10) }));
  }, []);

  const handleSubmit = useCallback(async () => {
    try {
      if (mode === 'create') {
        const { supabase } = await import('@/lib/supabase/client');
        const { data: activeSeason } = await supabase.from('seasons').select('id').eq('is_active', true).single();
        if (!activeSeason) {
          logger.error('No active season found', undefined, { component: 'useEventForm' });
          return;
        }
        await createTournament.mutateAsync({
          title: formData.title || '',
          date: formData.date || '',
          time_start: formData.time_start || '',
          time_end: formData.time_end || '',
          format: formData.format || '',
          store_id: formData.store_id || '',
          max_players: formData.max_players || 32,
          price: formData.price || 0,
          prize_pool: formData.prize_pool || '',
          description: formData.description || '',
          season_id: (activeSeason as { id: string }).id,
        });
      } else if (mode === 'edit' && initialData) {
        await updateTournament.mutateAsync({
          id: initialData.id,
          tournament: {
            title: formData.title,
            date: formData.date,
            time_start: formData.time_start,
            time_end: formData.time_end,
            format: formData.format,
            store_id: formData.store_id,
            max_players: formData.max_players,
            price: formData.price,
            prize_pool: formData.prize_pool,
            description: formData.description,
          }
        });
      }
    } catch (error) {
      logger.error(`Errore durante la ${mode === 'create' ? 'creazione' : 'modifica'} del torneo`, error, { component: 'useEventForm', mode });
    }
  }, [mode, formData, initialData, createTournament, updateTournament]);

  return { formData, stores, handleChange, handleNumberChange, handleSubmit };
}
