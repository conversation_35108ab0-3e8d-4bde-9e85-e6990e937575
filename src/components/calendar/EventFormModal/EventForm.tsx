import React from 'react';
import { Eye } from 'lucide-react';
import { Tournament } from '@/types/calendar';
import { DateTimeFields } from './FormFields/DateTimeFields';
import { StoreSelect } from './FormFields/StoreSelect';
import { ParticipantFields } from './FormFields/ParticipantFields';
import { PrizeFields } from './FormFields/PrizeFields';
import { Store } from './useEventForm';

interface EventFormProps {
  formData: Partial<Tournament>;
  stores: Store[];
  mode: 'create' | 'edit';
  onSubmit: (e: React.FormEvent) => void;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onNumberChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onPreview: () => void;
  onConfirm: () => void;
}

export function EventForm({
  formData,
  stores,
  mode,
  onChange,
  onNumberChange,
  onPreview,
  onConfirm,
}: EventFormProps) {
  const confirmationButtonText = mode === 'create' ? 'Crea Evento' : 'Salva Modifiche';
  const isValid = formData.title && formData.date && formData.store_id;

  return (
    <form className="space-y-4" onSubmit={(e) => e.preventDefault()}>
      <div className="space-y-2">
        <label className="block text-sm font-medium">Titolo</label>
        <input
          type="text"
          name="title"
          value={formData.title || ''}
          onChange={onChange}
          className="w-full px-3 py-2 bg-black/30 border border-blue-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-white"
          placeholder="Titolo del torneo"
        />
      </div>

      <DateTimeFields
        date={formData.date || ''}
        timeStart={formData.time_start || ''}
        timeEnd={formData.time_end || ''}
        onChange={onChange}
      />

      <StoreSelect
        storeId={formData.store_id || ''}
        stores={stores}
        onChange={onChange}
      />

      <ParticipantFields
        maxPlayers={formData.max_players || 30}
        price={formData.price || 5}
        onChange={onNumberChange}
      />

      <PrizeFields
        prizePool={formData.prize_pool || ''}
        description={formData.description || ''}
        onChange={onChange}
      />

      <div className="flex justify-between pt-4">
        <button
          type="button"
          onClick={onPreview}
          className="flex items-center gap-2 px-4 py-2 bg-black/30 hover:bg-blue-900/50 border border-blue-500/30 rounded-lg transition-colors"
        >
          <Eye size={16} />
          Preview
        </button>
        <button
          type="button"
          onClick={onConfirm}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-white font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:text-white"
          disabled={!isValid}
        >
          {confirmationButtonText}
        </button>
      </div>
    </form>
  );
}
